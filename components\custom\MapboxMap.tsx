"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useMapbox, type MapboxConfig } from '@/hooks/useMapbox';
import { cn } from '@/lib/utils';
import mapboxgl from 'mapbox-gl';
import Supercluster from 'supercluster';

export interface MapMarker {
  id: string;
  lng: number;
  lat: number;
  data: any;
}

export interface MapboxMapProps {
  className?: string;
  config?: MapboxConfig;
  markers?: MapMarker[];
  onMarkerClick?: (marker: MapMarker) => void;
  onMapMove?: (bounds: mapboxgl.LngLatBounds, zoom: number) => void;
  onMapLoad?: (map: mapboxgl.Map) => void;
  showClustering?: boolean;
  clusterRadius?: number;
  clusterMaxZoom?: number;
  height?: string;
}

/**
 * MapboxMap component with clustering support
 * Handles marker display, clustering, and map interactions
 */
export function MapboxMap({
  className,
  config,
  markers = [],
  onMarkerClick,
  onMapMove,
  onMapLoad,
  showClustering = true,
  clusterRadius = 50,
  clusterMaxZoom = 14,
  height = '400px',
}: MapboxMapProps) {
  const {
    map,
    mapContainer,
    isLoaded,
    error,
    setCenter,
    fitBounds,
    getBounds,
    getZoom,
  } = useMapbox(config);

  const [clusteredMarkers, setClusteredMarkers] = useState<any[]>([]);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const superclusterRef = useRef<Supercluster | null>(null);

  // Initialize Supercluster
  useEffect(() => {
    if (showClustering) {
      superclusterRef.current = new Supercluster({
        radius: clusterRadius,
        maxZoom: clusterMaxZoom,
        minZoom: 0,
        minPoints: 2,
      });
    }
  }, [showClustering, clusterRadius, clusterMaxZoom]);

  // Handle map load
  useEffect(() => {
    if (isLoaded && map && onMapLoad) {
      onMapLoad(map);
    }
  }, [isLoaded, map, onMapLoad]);

  // Handle map move events
  useEffect(() => {
    if (!map || !isLoaded) return;

    const handleMoveEnd = () => {
      const bounds = getBounds();
      const zoom = getZoom();
      if (bounds && onMapMove) {
        onMapMove(bounds, zoom);
      }
      updateMarkers();
    };

    map.on('moveend', handleMoveEnd);
    map.on('zoomend', handleMoveEnd);

    return () => {
      map.off('moveend', handleMoveEnd);
      map.off('zoomend', handleMoveEnd);
    };
  }, [map, isLoaded, getBounds, getZoom, onMapMove]);

  // Clear existing markers
  const clearMarkers = useCallback(() => {
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];
  }, []);

  // Create marker element
  const createMarkerElement = useCallback((isCluster: boolean, pointCount?: number) => {
    const el = document.createElement('div');
    el.className = cn(
      'cursor-pointer transition-transform hover:scale-110',
      isCluster 
        ? 'bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold border-2 border-background shadow-lg'
        : 'bg-destructive text-destructive-foreground rounded-full w-6 h-6 flex items-center justify-center border-2 border-background shadow-md'
    );

    if (isCluster && pointCount) {
      // Cluster styling based on point count
      const size = pointCount < 10 ? 30 : pointCount < 100 ? 40 : 50;
      el.style.width = `${size}px`;
      el.style.height = `${size}px`;
      el.textContent = pointCount.toString();
    } else {
      // Individual marker
      el.style.width = '24px';
      el.style.height = '24px';
      el.innerHTML = '📍';
    }

    return el;
  }, []);

  // Update markers based on current map view
  const updateMarkers = useCallback(() => {
    if (!map || !isLoaded) return;

    clearMarkers();

    if (!showClustering) {
      // Show all markers without clustering
      markers.forEach(marker => {
        const el = createMarkerElement(false);
        
        const mapboxMarker = new mapboxgl.Marker(el)
          .setLngLat([marker.lng, marker.lat])
          .addTo(map);

        // Add click handler
        el.addEventListener('click', () => {
          if (onMarkerClick) {
            onMarkerClick(marker);
          }
        });

        markersRef.current.push(mapboxMarker);
      });
      return;
    }

    // Use clustering
    if (!superclusterRef.current) return;

    // Convert markers to GeoJSON features
    const features = markers.map(marker => ({
      type: 'Feature' as const,
      properties: {
        cluster: false,
        markerId: marker.id,
        markerData: marker.data,
      },
      geometry: {
        type: 'Point' as const,
        coordinates: [marker.lng, marker.lat],
      },
    }));

    // Load features into supercluster
    superclusterRef.current.load(features);

    // Get current map bounds and zoom
    const bounds = getBounds();
    const zoom = getZoom();

    if (!bounds) return;

    // Get clusters for current view
    const clusters = superclusterRef.current.getClusters(
      [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
      Math.floor(zoom)
    );

    // Create markers for clusters and individual points
    clusters.forEach(cluster => {
      const [lng, lat] = cluster.geometry.coordinates;
      const isCluster = cluster.properties?.cluster;
      const pointCount = cluster.properties?.point_count;

      const el = createMarkerElement(isCluster, pointCount);
      
      const mapboxMarker = new mapboxgl.Marker(el)
        .setLngLat([lng, lat])
        .addTo(map);

      // Add click handler
      el.addEventListener('click', () => {
        if (isCluster) {
          // Zoom to cluster bounds
          const clusterId = cluster.properties?.cluster_id;
          if (clusterId !== undefined && superclusterRef.current) {
            const expansionZoom = superclusterRef.current.getClusterExpansionZoom(clusterId);
            setCenter([lng, lat], expansionZoom);
          }
        } else {
          // Handle individual marker click
          const originalMarker = markers.find(m => m.id === cluster.properties?.markerId);
          if (originalMarker && onMarkerClick) {
            onMarkerClick(originalMarker);
          }
        }
      });

      markersRef.current.push(mapboxMarker);
    });
  }, [map, isLoaded, markers, showClustering, clearMarkers, createMarkerElement, getBounds, getZoom, setCenter, onMarkerClick]);

  // Update markers when markers prop changes
  useEffect(() => {
    updateMarkers();
  }, [markers, updateMarkers]);

  // Fit map to markers when markers change
  useEffect(() => {
    if (!map || !isLoaded || markers.length === 0) return;

    if (markers.length === 1) {
      // Single marker - center on it
      const marker = markers[0];
      setCenter([marker.lng, marker.lat], 15);
    } else {
      // Multiple markers - fit bounds
      const bounds = new mapboxgl.LngLatBounds();
      markers.forEach(marker => {
        bounds.extend([marker.lng, marker.lat]);
      });
      fitBounds(bounds, { padding: 50 });
    }
  }, [map, isLoaded, markers, setCenter, fitBounds]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearMarkers();
    };
  }, [clearMarkers]);

  if (error) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground rounded-lg border',
          className
        )}
        style={{ height }}
      >
        <div className="text-center p-4">
          <p className="text-sm font-medium">Map Error</p>
          <p className="text-xs mt-1">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <div
        ref={mapContainer}
        className="w-full rounded-lg overflow-hidden"
        style={{ height }}
      />
      
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground mt-2">Loading map...</p>
          </div>
        </div>
      )}
    </div>
  );
}
