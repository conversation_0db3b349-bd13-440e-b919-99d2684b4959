"use client";

import React, { useState, useCallback } from 'react';
import { Search, MapPin, Filter, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useGeolocation } from '@/hooks/useGeolocation';
import { SearchFilters } from '@/hooks/useBusinessSearch';

export interface SearchInterfaceProps {
  className?: string;
  filters: SearchFilters;
  onFiltersChange: (filters: Partial<SearchFilters>) => void;
  onClearFilters: () => void;
  placeholder?: string;
  showLocationButton?: boolean;
  showFilterButton?: boolean;
  isLoading?: boolean;
}

/**
 * SearchInterface component for business directory search
 * Provides text search, location detection, and filter management
 */
export function SearchInterface({
  className,
  filters,
  onFiltersChange,
  onClearFilters,
  placeholder = "Search businesses...",
  showLocationButton = true,
  showFilterButton = true,
  isLoading = false,
}: SearchInterfaceProps) {
  const [searchValue, setSearchValue] = useState(filters.query || '');
  const { position, requestLocation, isLoading: locationLoading } = useGeolocation();

  // Handle search input change
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    onFiltersChange({ query: value || undefined });
  }, [onFiltersChange]);

  // Handle location request
  const handleLocationRequest = useCallback(() => {
    if (position) {
      // Use cached position
      onFiltersChange({
        userLocation: {
          lat: position.lat,
          lng: position.lng,
        },
      });
    } else {
      // Request new position
      requestLocation();
    }
  }, [position, requestLocation, onFiltersChange]);

  // Update user location when position changes
  React.useEffect(() => {
    if (position && !filters.userLocation) {
      onFiltersChange({
        userLocation: {
          lat: position.lat,
          lng: position.lng,
        },
      });
    }
  }, [position, filters.userLocation, onFiltersChange]);

  // Clear specific filter
  const clearFilter = useCallback((filterKey: keyof SearchFilters) => {
    onFiltersChange({ [filterKey]: undefined });
  }, [onFiltersChange]);

  // Count active filters
  const activeFilterCount = React.useMemo(() => {
    let count = 0;
    if (filters.categoryId) count++;
    if (filters.openNow) count++;
    if (filters.maxDistance) count++;
    if (filters.sortBy && filters.sortBy !== 'relevance') count++;
    return count;
  }, [filters]);

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main search bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10 pr-4 h-12 text-base"
          disabled={isLoading}
        />
        {searchValue && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            onClick={() => handleSearchChange('')}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Action buttons */}
      <div className="flex items-center gap-2 flex-wrap">
        {showLocationButton && (
          <Button
            variant={filters.userLocation ? "default" : "outline"}
            size="sm"
            onClick={handleLocationRequest}
            disabled={locationLoading || isLoading}
            className="flex items-center gap-2"
          >
            <MapPin className="h-4 w-4" />
            {locationLoading ? 'Locating...' : filters.userLocation ? 'Location Set' : 'Use My Location'}
          </Button>
        )}

        {showFilterButton && (
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 min-w-5 text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        )}

        {/* Clear all filters */}
        {(searchValue || activeFilterCount > 0 || filters.userLocation) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSearchValue('');
              onClearFilters();
            }}
            disabled={isLoading}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Active filters display */}
      {(filters.categoryId || filters.openNow || filters.maxDistance || filters.userLocation) && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          
          {filters.userLocation && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              Near me
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => clearFilter('userLocation')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.openNow && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Open now
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => clearFilter('openNow')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.maxDistance && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Within {filters.maxDistance}km
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => clearFilter('maxDistance')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.categoryId && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category selected
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => clearFilter('categoryId')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Search suggestions or recent searches could go here */}
      {searchValue && searchValue.length > 0 && searchValue.length < 3 && (
        <div className="text-sm text-muted-foreground">
          Type at least 3 characters to search
        </div>
      )}
    </div>
  );
}
